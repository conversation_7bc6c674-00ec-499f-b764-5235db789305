<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @package Dakoii_Provincial_Government_Theme
 */
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<a class="skip-link screen-reader-text" href="#content"><?php esc_html_e('Skip to content', 'dakoii-provincial-government-theme'); ?></a>

<!-- Header Banner -->
<?php 
$header_image = get_theme_mod('custom_header_image');
if ($header_image) : ?>
<div class="header-banner">
    <img src="<?php echo esc_url($header_image); ?>" alt="<?php echo esc_attr(get_bloginfo('name')); ?>" class="header-banner-image">
</div>
<?php endif; ?>

<!-- Navigation -->
<nav class="main-nav" id="site-navigation" aria-label="<?php esc_attr_e('Primary menu', 'dakoii-provincial-government-theme'); ?>">
    <div class="nav-container">
        <button class="menu-toggle" id="menu-toggle" onclick="toggleMobileMenu(this)">
            <span class="screen-reader-text"><?php esc_html_e('Primary Menu', 'dakoii-provincial-government-theme'); ?></span>
            ☰
        </button>
        <?php if (has_nav_menu('primary')) : ?>
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_id'        => 'primary-menu',
                'menu_class'     => 'nav-menu',
                'container'      => false,
                'fallback_cb'    => 'nols_espa_theme_fallback_menu',
            ));
            ?>
        <?php else : ?>
            <?php nols_espa_theme_fallback_menu(); ?>
        <?php endif; ?>
    </div>
</nav>

<script>
// Simple Mobile Menu Toggle Function
function toggleMobileMenu(button) {
    const navMenu = document.querySelector('.nav-menu');
    if (navMenu) {
        if (navMenu.classList.contains('is-open')) {
            navMenu.classList.remove('is-open');
            button.innerHTML = '<span class="screen-reader-text">Primary Menu</span>☰';
        } else {
            navMenu.classList.add('is-open');
            button.innerHTML = '<span class="screen-reader-text">Close Menu</span>✕';
        }
    }
}
</script>

<div id="content" class="site-content">

<?php
/**
 * Fallback menu when no menu is assigned
 */
function nols_espa_theme_fallback_menu() {
    $current_page_id = get_queried_object_id();
    ?>
    <ul class="nav-menu">

        <?php
        // Get pages for menu
        $pages = get_pages(array(
            'sort_order' => 'ASC',
            'sort_column' => 'menu_order',
            'number' => 5,
        ));

        foreach ($pages as $page) :
            $is_current = ($current_page_id == $page->ID);
        ?>
            <li class="nav-item <?php echo $is_current ? 'current_page_item' : ''; ?>">
                <a href="<?php echo esc_url(get_permalink($page->ID)); ?>">
                    <?php echo esc_html($page->post_title); ?>
                </a>
            </li>
        <?php endforeach; ?>
        
        <?php if (get_option('show_on_front') == 'posts') :
            $blog_page_id = get_option('page_for_posts');
            $is_blog_current = (is_home() || (is_single() && get_post_type() == 'post') || ($current_page_id == $blog_page_id));
        ?>
            <li class="nav-item <?php echo $is_blog_current ? 'current_page_item' : ''; ?>">
                <a href="<?php echo esc_url(get_permalink($blog_page_id)); ?>">
                    <?php esc_html_e('Blog', 'dakoii-provincial-government-theme'); ?>
                </a>
            </li>
        <?php endif; ?>

        <?php
        // Add categories to menu
        $categories = get_categories(array(
            'orderby' => 'name',
            'order'   => 'ASC',
            'number'  => 3,
        ));

        $current_category = get_queried_object();
        foreach ($categories as $category) :
            $is_category_current = (is_category() && isset($current_category->term_id) && $current_category->term_id == $category->term_id);
        ?>
            <li class="nav-item <?php echo $is_category_current ? 'current_page_item' : ''; ?>">
                <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>">
                    <?php echo esc_html($category->name); ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
    <?php
}
?>
